{"name": "form-generator", "version": "0.2.0", "private": true, "scripts": {"build": "vue-cli-service build", "build:report": "vue-cli-service build --report", "build:render": "vue-cli-service build --target lib --name form-gen-render --dest ./src/components/render/lib/ ./src/components/render/render.js", "build:parser": "vue-cli-service build --target lib --name form-gen-parser --dest ./src/components/parser/lib/ ./src/components/parser/index.js", "build:tinymce": "vue-cli-service build --target lib --name form-gen-tinymce --dest ./src/components/tinymce/lib/ ./src/components/tinymce/index.js", "lint": "vue-cli-service lint", "dev": "vue-cli-service serve"}, "dependencies": {"@babel/parser": "^7.7.4", "axios": "^0.19.2", "clipboard": "^2.0.4", "core-js": "^3.6.5", "file-saver": "^2.0.2", "throttle-debounce": "^2.1.0", "vue": "^2.6.11", "vuedraggable": "^2.23.2"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.4.0", "@vue/cli-plugin-eslint": "~4.4.0", "@vue/cli-service": "~4.4.0", "babel-eslint": "^10.1.0", "eslint": "^6.8.0", "eslint-config-airbnb-base": "^14.0.0", "eslint-plugin-import": "^2.20.0", "eslint-plugin-vue": "^6.2.2", "sass": "^1.23.7", "sass-loader": "^8.0.0", "svg-sprite-loader": "^4.1.6", "vue-template-compiler": "^2.6.11"}, "homepage": "https://jakhuang.github.io/form-generator"}